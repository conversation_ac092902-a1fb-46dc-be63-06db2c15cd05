2025-07-16 04:57:13 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 04:57:13 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 04:57:13 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:57:13 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:57:13 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:57:14 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:57:14 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 04:57:14 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 04:57:14 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:57:14 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:57:14 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:57:14 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:57:14 - [main] [INFO] - Application starting...
2025-07-16 04:57:17 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 04:57:17 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 04:57:17 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 04:57:17 - [flatmate.src.fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-16 04:57:17 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-16 04:57:17 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:57:17 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 364MB cache limit
2025-07-16 04:57:17 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up home module
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-16 04:57:17 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-16 04:57:17 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-16 04:57:17 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-16 04:57:17 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-16 04:57:17 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-16 04:57:18 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:57:18 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 311MB cache limit
2025-07-16 04:57:18 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-16 04:57:18 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-16 04:57:18 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-16 04:57:18 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-16 04:57:18 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-16 04:57:18 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.067s
2025-07-16 04:57:18 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-16 04:57:18 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-16 04:57:18 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-16 04:57:18 - [flatmate.src.fm.module_coordinator] [INFO] - All modules created and configured
2025-07-16 04:57:18 - [flatmate.src.fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-07-16 04:57:18 - [main] [INFO] - 
=== Initializing Database Cache ===
2025-07-16 04:57:18 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:57:18 - [flatmate.src.fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 310MB cache limit
2025-07-16 04:57:18 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-16 04:57:18 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-16 04:57:19 - [flatmate.src.fm.core.data_services.cache.db_caching] [DEBUG] - Cached database statistics: 2099 transactions, 3 accounts
2025-07-16 04:57:19 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 1.2s
2025-07-16 04:57:19 - [main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-16 04:57:19 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-16 04:57:19 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from None to home
2025-07-16 04:57:19 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:57:19 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 04:57:19 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 04:57:19 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 04:57:19 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 04:57:19 - [flatmate.src.fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 04:57:19 - [main] [INFO] - 
=== Application Ready ===
2025-07-16 04:57:26 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-07-16 04:57:26 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-16 04:57:26 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-07-16 04:57:26 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 04:57:26 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 04:57:26 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 04:57:26 - [flatmate.src.fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 04:57:29 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 04:57:29 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 04:57:29 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 04:57:29 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 04:57:29 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 04:57:29 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 04:57:29 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 04:57:29 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:57:29 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 04:57:32 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-07-16 04:57:32 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter already hidden
2025-07-16 04:57:32 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - UpdateDataView already set up in window, re-adding panels to layouts
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for UpdateDataView
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 04:57:32 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 04:57:32 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 04:57:32 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 04:57:32 - [flatmate.src.fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 04:57:34 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 04:57:34 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 04:57:34 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 04:57:34 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 04:57:34 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 04:57:34 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 04:57:34 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 04:57:34 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:57:34 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 04:57:35 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-07-16 04:57:35 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter already hidden
2025-07-16 04:57:35 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - UpdateDataView already set up in window, re-adding panels to layouts
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for UpdateDataView
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 04:57:35 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 04:57:35 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 04:57:35 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 04:57:35 - [flatmate.src.fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 04:57:43 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 04:57:43 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 04:57:43 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 04:57:43 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 04:57:43 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 04:57:43 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 04:57:43 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 04:57:43 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:57:43 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 04:57:46 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from HomePresenter to home
2025-07-16 04:57:46 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 04:57:46 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter already hidden
2025-07-16 04:57:46 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:57:46 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 04:57:48 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-07-16 04:57:48 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter already hidden
2025-07-16 04:57:48 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - UpdateDataView already set up in window, re-adding panels to layouts
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for UpdateDataView
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 04:57:48 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 04:57:48 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 04:57:48 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 04:57:48 - [flatmate.src.fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 04:57:52 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 04:57:52 - [flatmate.src.fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 04:57:52 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 04:57:52 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 04:57:52 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 04:57:52 - [flatmate.src.fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 04:57:52 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 04:57:52 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:57:52 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
