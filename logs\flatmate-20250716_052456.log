2025-07-16 05:24:56 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 05:24:57 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 05:24:57 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 05:24:57 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 05:24:57 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 05:24:57 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 05:24:57 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 05:24:57 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 05:24:57 - [fm.main] [INFO] - Application starting...
2025-07-16 05:24:59 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 05:24:59 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 05:24:59 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 05:24:59 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-16 05:24:59 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-16 05:24:59 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 05:24:59 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 410MB cache limit
2025-07-16 05:24:59 - [fm.module_coordinator] [INFO] - Setting up home module
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-16 05:24:59 - [fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-16 05:24:59 - [fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-16 05:24:59 - [fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-16 05:24:59 - [fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-16 05:24:59 - [fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-16 05:24:59 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 05:24:59 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 413MB cache limit
2025-07-16 05:24:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-16 05:24:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-16 05:24:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-16 05:24:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-16 05:24:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-16 05:24:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.027s
2025-07-16 05:24:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-16 05:24:59 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-16 05:24:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - About to call _load_data_during_setup()
2025-07-16 05:24:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Loading data during setup (eager loading)
2025-07-16 05:24:59 - [fm.core.config.base_local_config_v2] [DEBUG] - Set new config key in core config: categorize.database.last_account = None (Source: cat_presenter.py:_load_data_during_setup)
2025-07-16 05:24:59 - [fm.modules.categorize.cat_presenter] [INFO] - Auto-loading ALL transactions from database...
2025-07-16 05:24:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._handle_load_db
2025-07-16 05:24:59 - [fm.modules.categorize.cat_presenter] [INFO] - Loading transactions from database for categorisation…
2025-07-16 05:24:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Filters: None
2025-07-16 05:24:59 - [fm.modules.categorize.cat_presenter] [DEBUG] - Fetching transactions with filters: {}
2025-07-16 05:24:59 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Data Retrieval from Cache
2025-07-16 05:24:59 - [fm.core.data_services.cache.db_caching] [DEBUG] - Cache not available, falling back to database query with filters: {}
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [INFO] - Retrieved 2099 transactions as DataFrame
2025-07-16 05:25:00 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Data Retrieval from Cache took 0.404s
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame shape: (2099, 30), empty: False
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - First few rows:
              account  amount  ...  tp_ref unique_id
0  38-9004-0646977-04  -30.44  ...    None      None
1  38-9004-0646977-04   -9.40  ...    None      None
2  38-9004-0646977-04  -18.99  ...    None      None
3  38-9004-0646977-04  -20.50  ...    None      None
4  38-9004-0646977-00 -100.00  ...    None      None

[5 rows x 30 columns]
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - Using DataFrame with shape: (2099, 30)
2025-07-16 05:25:00 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Transaction Categorization
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applying categorization to transactions...
2025-07-16 05:25:00 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Transaction Categorization took 0.047s
2025-07-16 05:25:00 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: CategorizePresenter._apply_default_sorting
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - Applied default sorting: date (descending)
2025-07-16 05:25:00 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._apply_default_sorting took 0.003s
2025-07-16 05:25:00 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: Table View Data Setting
2025-07-16 05:25:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - Setting DataFrame with 2099 transactions to view
2025-07-16 05:25:00 - [fm.modules.categorize._view.cat_view] [DEBUG] - CatView setting dataframe: 2099 rows
2025-07-16 05:25:00 - [fm.modules.categorize._view.cat_view] [DEBUG] - DataFrame columns: ['account', 'amount', 'balance', 'category', 'credit_amount', 'date', 'db_uid', 'debit_amount', 'details', 'empty', 'hash', 'import_date', 'modified_date', 'notes', 'op_account', 'op_code', 'op_name', 'op_part', 'op_ref', 'payment_type', 'source_bank', 'source_filename', 'source_type', 'source_uid', 'statement_date', 'tags', 'tp_code', 'tp_part', 'tp_ref', 'unique_id']
2025-07-16 05:25:00 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting transactions: 2099 rows
2025-07-16 05:25:02 - [fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Displaying columns: ['Account', 'Date', 'Details', 'Amount', 'Category', 'Tags', 'Notes']
2025-07-16 05:25:02 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: Table View Data Setting took 2.439s
2025-07-16 05:25:02 - [fm.modules.categorize.cat_presenter] [INFO] - Successfully loaded and displayed 2099 transactions in 2.9s (712.4 txns/s)
2025-07-16 05:25:02 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: CategorizePresenter._handle_load_db took 2.953s
2025-07-16 05:25:02 - [fm.modules.categorize.cat_presenter] [DEBUG] - Data loading during setup complete
2025-07-16 05:25:02 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-16 05:25:02 - [fm.module_coordinator] [INFO] - All modules created and configured
2025-07-16 05:25:02 - [fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-07-16 05:25:02 - [fm.main] [INFO] - 
=== Initializing Database Cache ===
2025-07-16 05:25:02 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 05:25:02 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 401MB cache limit
2025-07-16 05:25:02 - [fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-16 05:25:02 - [fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-16 05:25:03 - [fm.core.data_services.cache.db_caching] [DEBUG] - Cached database statistics: 2099 transactions, 3 accounts
2025-07-16 05:25:03 - [fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 0.4s
2025-07-16 05:25:03 - [fm.main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-16 05:25:03 - [fm.module_coordinator] [INFO] - Starting Application
2025-07-16 05:25:03 - [fm.module_coordinator] [INFO] - Transitioning from None to home
2025-07-16 05:25:03 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 05:25:03 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 05:25:03 - [fm.modules.base.base_module_view] [INFO] - Setting up HomeView in Main Window
2025-07-16 05:25:03 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-16 05:25:03 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-16 05:25:03 - [fm.modules.base.base_module_view] [INFO] - HomeView setup complete
2025-07-16 05:25:03 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 05:25:03 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 05:25:03 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 05:25:03 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 05:25:03 - [fm.main] [INFO] - 
=== Application Ready ===
2025-07-16 05:26:00 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to categorize
2025-07-16 05:26:00 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 05:26:00 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-16 05:26:00 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-16 05:26:00 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:00 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:00 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-16 05:26:00 - [fm.module_coordinator] [DEBUG] - Showing categorize module
2025-07-16 05:26:00 - [fm.modules.base.base_presenter] [INFO] - Showing CategorizePresenter
2025-07-16 05:26:00 - [fm.modules.base.base_module_view] [INFO] - Setting up CatView in Main Window
2025-07-16 05:26:00 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-16 05:26:01 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-16 05:26:01 - [fm.modules.base.base_module_view] [INFO] - CatView setup complete
2025-07-16 05:26:01 - [fm.modules.categorize.cat_presenter] [DEBUG] - Refreshing Categorize content (lightweight)
2025-07-16 05:26:01 - [fm.modules.categorize.cat_presenter] [DEBUG] - Categorize content refresh complete
2025-07-16 05:26:01 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now visible
2025-07-16 05:26:01 - [fm.module_coordinator] [DEBUG] - No navigation button found for module 'categorize' - this is fine
2025-07-16 05:26:01 - [fm.module_coordinator] [INFO] - Successfully transitioned to categorize
2025-07-16 05:26:13 - [fm.module_coordinator] [INFO] - Transitioning from CategorizePresenter to home
2025-07-16 05:26:13 - [fm.module_coordinator] [DEBUG] - Hiding CategorizePresenter
2025-07-16 05:26:13 - [fm.modules.base.base_presenter] [INFO] - Hiding CategorizePresenter
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up CatView from main window
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:13 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now hidden
2025-07-16 05:26:13 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 05:26:13 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - HomeView already set up in window, re-adding panels to layouts
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for HomeView
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:26:13 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:26:13 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 05:26:13 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 05:26:13 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 05:26:13 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 05:26:16 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to update_data
2025-07-16 05:26:16 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 05:26:16 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:16 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-16 05:26:16 - [fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 05:26:16 - [fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [INFO] - Setting up UpdateDataView in Main Window
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Left Panel
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [DEBUG] - Setting up Center Panel
2025-07-16 05:26:16 - [fm.modules.base.base_module_view] [INFO] - UpdateDataView setup complete
2025-07-16 05:26:16 - [fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 05:26:16 - [fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 05:26:16 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 05:26:16 - [fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 05:26:18 - [fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 05:26:18 - [fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 05:26:18 - [fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:18 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 05:26:18 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 05:26:18 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - HomeView already set up in window, re-adding panels to layouts
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for HomeView
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:26:18 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:26:18 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 05:26:18 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 05:26:18 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 05:26:18 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 05:26:33 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to categorize
2025-07-16 05:26:33 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 05:26:33 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:33 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-16 05:26:33 - [fm.module_coordinator] [DEBUG] - Showing categorize module
2025-07-16 05:26:33 - [fm.modules.base.base_presenter] [INFO] - Showing CategorizePresenter
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - CatView already set up in window, re-adding panels to layouts
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for CatView
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:26:33 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:26:33 - [fm.modules.categorize.cat_presenter] [DEBUG] - Refreshing Categorize content (lightweight)
2025-07-16 05:26:33 - [fm.modules.categorize.cat_presenter] [DEBUG] - Categorize content refresh complete
2025-07-16 05:26:33 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now visible
2025-07-16 05:26:33 - [fm.module_coordinator] [DEBUG] - No navigation button found for module 'categorize' - this is fine
2025-07-16 05:26:33 - [fm.module_coordinator] [INFO] - Successfully transitioned to categorize
2025-07-16 05:26:41 - [fm.module_coordinator] [INFO] - Transitioning from CategorizePresenter to update_data
2025-07-16 05:26:41 - [fm.module_coordinator] [DEBUG] - Hiding CategorizePresenter
2025-07-16 05:26:41 - [fm.modules.base.base_presenter] [INFO] - Hiding CategorizePresenter
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up CatView from main window
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:41 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now hidden
2025-07-16 05:26:41 - [fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 05:26:41 - [fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - UpdateDataView already set up in window, re-adding panels to layouts
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for UpdateDataView
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:26:41 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:26:41 - [fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 05:26:41 - [fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 05:26:41 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 05:26:41 - [fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 05:26:44 - [fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to view_data
2025-07-16 05:26:44 - [fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 05:26:44 - [fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:26:44 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 05:26:44 - [fm.module_coordinator] [ERROR] - No module found for 'view_data'. Available modules: ['home', 'update_data', 'categorize']
NoneType: None
2025-07-16 05:26:44 - [fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 05:26:44 - [fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 05:26:44 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter already hidden
2025-07-16 05:26:44 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 05:26:44 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - HomeView already set up in window, re-adding panels to layouts
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for HomeView
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:26:44 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:26:44 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 05:26:44 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 05:26:44 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 05:26:44 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 05:28:00 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to categorize
2025-07-16 05:28:00 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 05:28:00 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:28:00 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-16 05:28:00 - [fm.module_coordinator] [DEBUG] - Showing categorize module
2025-07-16 05:28:00 - [fm.modules.base.base_presenter] [INFO] - Showing CategorizePresenter
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - CatView already set up in window, re-adding panels to layouts
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for CatView
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:28:00 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:28:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - Refreshing Categorize content (lightweight)
2025-07-16 05:28:00 - [fm.modules.categorize.cat_presenter] [DEBUG] - Categorize content refresh complete
2025-07-16 05:28:00 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now visible
2025-07-16 05:28:00 - [fm.module_coordinator] [DEBUG] - No navigation button found for module 'categorize' - this is fine
2025-07-16 05:28:00 - [fm.module_coordinator] [INFO] - Successfully transitioned to categorize
2025-07-16 05:28:18 - [fm.module_coordinator] [INFO] - Transitioning from CategorizePresenter to update_data
2025-07-16 05:28:18 - [fm.module_coordinator] [DEBUG] - Hiding CategorizePresenter
2025-07-16 05:28:18 - [fm.modules.base.base_presenter] [INFO] - Hiding CategorizePresenter
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up CatView from main window
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:28:18 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now hidden
2025-07-16 05:28:18 - [fm.module_coordinator] [DEBUG] - Showing update_data module
2025-07-16 05:28:18 - [fm.modules.base.base_presenter] [INFO] - Showing UpdateDataPresenter
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - UpdateDataView already set up in window, re-adding panels to layouts
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for UpdateDataView
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:28:18 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:28:18 - [fm.modules.update_data.ud_presenter] [DEBUG] - Refreshing UpdateData content
2025-07-16 05:28:18 - [fm.modules.update_data.ud_presenter] [DEBUG] - UpdateData content refresh complete
2025-07-16 05:28:18 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now visible
2025-07-16 05:28:18 - [fm.module_coordinator] [INFO] - Successfully transitioned to update_data
2025-07-16 05:29:13 - [fm.module_coordinator] [INFO] - Transitioning from UpdateDataPresenter to home
2025-07-16 05:29:13 - [fm.module_coordinator] [DEBUG] - Hiding UpdateDataPresenter
2025-07-16 05:29:13 - [fm.modules.base.base_presenter] [INFO] - Hiding UpdateDataPresenter
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up UpdateDataView from main window
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:29:13 - [fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter is now hidden
2025-07-16 05:29:13 - [fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 05:29:13 - [fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - HomeView already set up in window, re-adding panels to layouts
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for HomeView
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:29:13 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:29:13 - [fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 05:29:13 - [fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 05:29:13 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 05:29:13 - [fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 05:29:19 - [fm.module_coordinator] [INFO] - Transitioning from HomePresenter to categorize
2025-07-16 05:29:19 - [fm.module_coordinator] [DEBUG] - Hiding HomePresenter
2025-07-16 05:29:19 - [fm.modules.base.base_presenter] [INFO] - Hiding HomePresenter
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - Cleaning up HomeView from main window
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - Removed left panel from layout
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - Removed center panel from layout
2025-07-16 05:29:19 - [fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now hidden
2025-07-16 05:29:19 - [fm.module_coordinator] [DEBUG] - Showing categorize module
2025-07-16 05:29:19 - [fm.modules.base.base_presenter] [INFO] - Showing CategorizePresenter
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - CatView already set up in window, re-adding panels to layouts
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - Adding panels to layouts for CatView
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - Added left panel to layout
2025-07-16 05:29:19 - [fm.modules.base.base_module_view] [DEBUG] - Added center panel to layout
2025-07-16 05:29:19 - [fm.modules.categorize.cat_presenter] [DEBUG] - Refreshing Categorize content (lightweight)
2025-07-16 05:29:19 - [fm.modules.categorize.cat_presenter] [DEBUG] - Categorize content refresh complete
2025-07-16 05:29:19 - [fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter is now visible
2025-07-16 05:29:19 - [fm.module_coordinator] [DEBUG] - No navigation button found for module 'categorize' - this is fine
2025-07-16 05:29:19 - [fm.module_coordinator] [INFO] - Successfully transitioned to categorize
