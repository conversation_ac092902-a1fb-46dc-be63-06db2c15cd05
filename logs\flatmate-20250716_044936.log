2025-07-16 04:49:36 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 04:49:36 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 04:49:37 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:49:37 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:49:37 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:49:38 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:49:39 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 04:49:39 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 04:49:43 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:49:43 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:49:43 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:49:44 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:49:47 - [main] [INFO] - Application starting...
2025-07-16 04:49:59 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 04:49:59 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 04:49:59 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 04:49:59 - [flatmate.src.fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-16 04:50:00 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-16 04:50:00 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 04:50:00 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-16 04:50:00 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-16 04:50:00 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:50:00 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 201MB cache limit
2025-07-16 04:50:00 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up home module
2025-07-16 04:50:01 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-16 04:50:01 - [main] [CRITICAL] - Fatal error during application initialization: HomeView.__init__() got an unexpected keyword argument 'gui_config'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 58, in initialize_application
    coordinator.initialize_modules()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 63, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 79, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 38, in _create_view
    return HomeView(gui_config=self.gui_config, gui_keys=self.gui_keys)
TypeError: HomeView.__init__() got an unexpected keyword argument 'gui_config'
2025-07-16 04:50:01 - [main] [CRITICAL] - Exception details: Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 58, in initialize_application
    coordinator.initialize_modules()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 63, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 79, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 38, in _create_view
    return HomeView(gui_config=self.gui_config, gui_keys=self.gui_keys)
TypeError: HomeView.__init__() got an unexpected keyword argument 'gui_config'
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 58, in initialize_application
    coordinator.initialize_modules()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 63, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 79, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 38, in _create_view
    return HomeView(gui_config=self.gui_config, gui_keys=self.gui_keys)
TypeError: HomeView.__init__() got an unexpected keyword argument 'gui_config'
