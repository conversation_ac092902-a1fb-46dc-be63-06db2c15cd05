2025-07-16 04:32:36 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 04:32:36 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 04:32:36 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:32:36 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:32:36 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:32:37 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:32:37 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 04:32:37 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 04:32:37 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:32:37 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:32:37 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:32:37 - [flatmate.src.fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:32:37 - [main] [INFO] - Application starting...
2025-07-16 04:32:39 - [main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 04:32:39 - [flatmate.src.fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 04:32:39 - [flatmate.src.fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 04:32:39 - [flatmate.src.fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-16 04:32:39 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-16 04:32:39 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:32:39 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 442MB cache limit
2025-07-16 04:32:39 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up home module
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-16 04:32:39 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Connecting Home View signals
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter setup complete
2025-07-16 04:32:39 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up update_data module
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up UpdateDataPresenter
2025-07-16 04:32:39 - [flatmate.src.fm.modules.update_data.ud_presenter] [DEBUG] - Signals connected
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - UpdateDataPresenter setup complete
2025-07-16 04:32:39 - [flatmate.src.fm.module_coordinator] [INFO] - Setting up categorize module
2025-07-16 04:32:39 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Setting up CategorizePresenter
2025-07-16 04:32:39 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:32:39 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 397MB cache limit
2025-07-16 04:32:39 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Initializing TransactionViewPanel
2025-07-16 04:32:39 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING START: TransactionViewPanel._init_ui
2025-07-16 04:32:39 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Setting up TransactionViewPanel UI
2025-07-16 04:32:39 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Creating CustomTableView_v2 for transactions
2025-07-16 04:32:40 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel UI setup complete
2025-07-16 04:32:40 - [fm.core.utils.timing_decorator] [INFO] - ⏱️  TIMING END: TransactionViewPanel._init_ui took 0.030s
2025-07-16 04:32:40 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - Connecting TransactionViewPanel signals
2025-07-16 04:32:40 - [flatmate.src.fm.modules.categorize._view.components.center_panel.transaction_view_panel] [DEBUG] - TransactionViewPanel signals connected
2025-07-16 04:32:40 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - CategorizePresenter setup complete
2025-07-16 04:32:40 - [flatmate.src.fm.module_coordinator] [INFO] - All modules created and configured
2025-07-16 04:32:40 - [flatmate.src.fm.module_coordinator] [DEBUG] - Available modules: ['home', 'update_data', 'categorize']
2025-07-16 04:32:40 - [main] [INFO] - 
=== Initializing Database Cache ===
2025-07-16 04:32:40 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:32:40 - [flatmate.src.fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 383MB cache limit
2025-07-16 04:32:40 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Initializing database cache...
2025-07-16 04:32:40 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Loading all transactions from database with all columns...
2025-07-16 04:32:40 - [flatmate.src.fm.core.data_services.cache.db_caching] [DEBUG] - Cached database statistics: 2099 transactions, 3 accounts
2025-07-16 04:32:40 - [flatmate.src.fm.core.data_services.cache.db_caching] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory, 0.9s
2025-07-16 04:32:40 - [main] [INFO] - Database cache initialized: 2099 transactions, 2.3MB memory usage
2025-07-16 04:32:40 - [flatmate.src.fm.module_coordinator] [INFO] - Starting Application
2025-07-16 04:32:40 - [flatmate.src.fm.module_coordinator] [INFO] - Transitioning from None to home
2025-07-16 04:32:40 - [flatmate.src.fm.module_coordinator] [DEBUG] - Showing home module
2025-07-16 04:32:40 - [flatmate.src.fm.modules.base.base_presenter] [INFO] - Showing HomePresenter
2025-07-16 04:32:40 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Refreshing Home content
2025-07-16 04:32:40 - [flatmate.src.fm.modules.home.home_presenter] [DEBUG] - Home content refresh complete
2025-07-16 04:32:40 - [flatmate.src.fm.modules.base.base_presenter] [DEBUG] - HomePresenter is now visible
2025-07-16 04:32:40 - [flatmate.src.fm.module_coordinator] [INFO] - Successfully transitioned to home
2025-07-16 04:32:40 - [main] [INFO] - 
=== Application Ready ===
