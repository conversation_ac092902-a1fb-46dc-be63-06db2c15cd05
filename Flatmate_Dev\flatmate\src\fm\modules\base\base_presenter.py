"""
Base presenter implementation for eager loading pattern.
Provides standard lifecycle management for all module presenters.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from ...core.services.logger import log


class BasePresenter(ABC):
    """Base class for all module presenters implementing eager loading lifecycle.
    
    This class enforces a consistent lifecycle across all modules:
    1. setup() - One-time initialization at app startup
    2. show() - Called every time module becomes visible
    3. hide() - Called every time module is hidden
    
    This pattern prevents Qt widget deletion crashes and enables instant navigation.
    """
    
    def __init__(self, main_window, gui_config=None, gui_keys=None):
        """Initialize the base presenter.

        Args:
            main_window: The main window instance that serves as the UI container
            gui_config: Injected GUI configuration service
            gui_keys: Injected GUI configuration keys
        """
        self.main_window = main_window
        self.gui_config = gui_config
        self.gui_keys = gui_keys
        self.view = None
        self._is_setup = False
        self._is_visible = False

        # This will be connected by ModuleCoordinator
        self.request_transition = None

        log.debug(f"Initialized {self.__class__.__name__}")
    
    @abstractmethod
    def _create_view(self):
        """Create the view instance. Called once during setup.
        
        Returns:
            The view instance for this module
        """
        pass
    
    @abstractmethod
    def _connect_signals(self):
        """Connect view signals to handlers. Called once during setup.
        
        This method should connect all view signals to their respective handlers.
        It's called after the view is created but before the module is shown.
        """
        pass
    
    def setup(self):
        """One-time setup: create view, connect signals, build UI.
        
        This method is called once at application startup for all modules.
        It performs all the expensive one-time operations:
        - Creates the view instance
        - Connects signals
        - Builds UI widgets (but doesn't place them in main window yet)
        
        This method is idempotent - safe to call multiple times.
        """
        if self._is_setup:
            log.debug(f"{self.__class__.__name__} already set up, skipping")
            return
            
        log.info(f"Setting up {self.__class__.__name__}")
        
        # Create the view instance
        self.view = self._create_view()
        if not self.view:
            raise RuntimeError(f"{self.__class__.__name__}._create_view() returned None")
        
        # Connect signals
        self._connect_signals()
        
        self._is_setup = True
        log.debug(f"{self.__class__.__name__} setup complete")
    
    def show(self, **params):
        """Show module and refresh content.
        
        This method is called every time the module becomes visible.
        It handles:
        - Ensuring setup is complete
        - Placing widgets in main window
        - Refreshing content based on parameters
        
        Args:
            **params: Optional parameters to pass to content refresh
        """
        if not self._is_setup:
            log.warning(f"{self.__class__.__name__} not set up, calling setup() first")
            self.setup()
        
        log.info(f"Showing {self.__class__.__name__}")
        
        # Set up view in main window (this handles widget placement)
        # BaseModuleView.setup_in_main_window() is now idempotent
        self.view.setup_in_main_window(self.main_window)
        
        # Refresh content for this navigation
        self._refresh_content(**params)
        
        self._is_visible = True
        log.debug(f"{self.__class__.__name__} is now visible")
    
    def hide(self):
        """Hide module but preserve state.
        
        This method is called when navigating away from the module.
        It removes widgets from the main window without destroying them,
        preventing Qt object deletion crashes.
        """
        if not self._is_visible:
            log.debug(f"{self.__class__.__name__} already hidden")
            return
            
        log.info(f"Hiding {self.__class__.__name__}")
        
        # Remove widgets from main window without destroying them
        if self.view and hasattr(self.view, 'cleanup_from_main_window'):
            self.view.cleanup_from_main_window()
        
        self._is_visible = False
        log.debug(f"{self.__class__.__name__} is now hidden")
    
    def _refresh_content(self, **params):
        """Override to refresh module content when shown.
        
        This method is called every time the module is shown.
        Subclasses should override this to implement module-specific
        content refresh logic.
        
        Args:
            **params: Optional parameters passed from navigation
        """
        # Default implementation does nothing
        # Subclasses should override this method
        pass
    
    def cleanup(self):
        """Final cleanup when module is destroyed.
        
        This method is called when the application is shutting down
        or when modules need to be completely destroyed.
        """
        log.info(f"Cleaning up {self.__class__.__name__}")
        
        if self.view:
            # Use the view's cleanup method if available
            if hasattr(self.view, 'cleanup'):
                self.view.cleanup()
            self.view = None
        
        self._is_setup = False
        self._is_visible = False
        
        log.debug(f"{self.__class__.__name__} cleanup complete")
    
    # Properties for debugging and monitoring
    @property
    def is_setup(self) -> bool:
        """Check if module is set up."""
        return self._is_setup
    
    @property
    def is_visible(self) -> bool:
        """Check if module is currently visible."""
        return self._is_visible
    
    def __repr__(self):
        """String representation for debugging."""
        return f"{self.__class__.__name__}(setup={self._is_setup}, visible={self._is_visible})"
