"""
Module coordinator for the FlatMate application.
Manages module initialization, transitions, and lifecycle.
"""

from typing import Dict, Any, Optional, Callable
from functools import partial
from .core.config import config  # Add config import
from .core.config.keys import ConfigKeys  # Add config keys import
from .core.services.logger import log
from .modules.home.home_presenter import HomePresenter
from .modules.update_data.ud_presenter import UpdateDataPresenter
from .modules.categorize.cat_presenter import CategorizePresenter


class ModuleCoordinator:
    """Coordinates all modules in the application."""
    
    # Mapping from navigation button IDs to module IDs
    # This is the reverse of NavPane.MODULE_TO_NAV_MAP
    NAV_TO_MODULE_MAP = {
        'import_data': 'update_data',  # Nav button is 'import_data' but module is 'update_data'
    }
    
    def __init__(self, main_window):
        """Initialize the coordinator.

        Args:
            main_window: The main window instance that serves as a vessel
        """
        log.info("Initializing Module Coordinator")
        self.main_window = main_window
        self.current_module = None

        # Changed from module_factories to modules for eager loading
        self.modules = {}

        # Load module preferences using new config
        self.recent_modules = config.get_value(ConfigKeys.Window.RECENT_FILES, default=[])
        log.debug(f"Loaded recent modules: {self.recent_modules}")
        
    def initialize_modules(self):
        """Create and setup all modules at startup (eager loading)."""
        log.info("Creating all modules (eager loading)")

        # Import GUI config here to inject as dependency (avoids circular imports)
        from .gui.config.gui_config import gui_config
        from .gui.config.gui_keys import GuiKeys

        # Create all module instances with injected dependencies
        self.modules = {
            'home': HomePresenter(self.main_window, gui_config, GuiKeys),
            'update_data': UpdateDataPresenter(self.main_window, gui_config, GuiKeys),
            'categorize': CategorizePresenter(self.main_window, gui_config, GuiKeys)
        }

        # Setup all modules (one-time UI creation)
        for module_name, module in self.modules.items():
            log.info(f"Setting up {module_name} module")

            # Call setup method (from BasePresenter)
            if hasattr(module, 'setup'):
                module.setup()
            else:
                log.warning(f"{module_name} module doesn't have setup() method - may not inherit from BasePresenter")

            # Connect transitions
            self._connect_module_transitions(module)

        log.info("All modules created and configured")
        log.debug(f"Available modules: {list(self.modules.keys())}")
    
    def start(self):
        """Start the application with the home module."""
        log.info("Starting Application")
        self.transition_to('home')
    
    def _connect_module_transitions(self, module):
        """Connect a module's request_transition to our transition_to method.
        
        Args:
            module: The module instance to connect
        """
        if hasattr(module, 'request_transition'):
            # Create a bound method for this specific module's transitions
            module.request_transition = partial(self.transition_to)
            print(f"Connected transitions for {type(module).__name__}")
    
    def transition_to(self, module_name: str, **params: Dict[str, Any]):
        """Transition between pre-built modules (eager loading).

        Args:
            module_name: Name of the module to transition to
            **params: Optional parameters to pass to the module
        """
        # Map navigation button ID to module ID if needed
        actual_module_name = self.NAV_TO_MODULE_MAP.get(module_name, module_name)
        log.info(f"Transitioning from {type(self.current_module).__name__ if self.current_module else 'None'} to {actual_module_name}")

        # Use the mapped module name for the rest of the method
        module_name = actual_module_name

        # Hide current module (don't destroy it)
        if self.current_module:
            log.debug(f"Hiding {type(self.current_module).__name__}")

            # Call hide method (from BasePresenter)
            if hasattr(self.current_module, 'hide'):
                self.current_module.hide()
            else:
                log.warning(f"Current module doesn't have hide() method")

        # Show new module
        if module_name in self.modules:
            log.debug(f"Showing {module_name} module")
            self.current_module = self.modules[module_name]

            # Call show method (from BasePresenter)
            if hasattr(self.current_module, 'show'):
                self.current_module.show(**params)
            else:
                log.error(f"{module_name} module doesn't have show() method - may not inherit from BasePresenter")
                return

            # Update NavPane to reflect current module (visual only)
            self._update_navigation_highlight(module_name)

            # Save to recent modules using new config
            if module_name not in self.recent_modules:
                self.recent_modules.append(module_name)
                config.set_value(ConfigKeys.Window.RECENT_FILES, self.recent_modules)

            log.info(f"Successfully transitioned to {module_name}")
        else:
            log.error(f"No module found for '{module_name}'. Available modules: {list(self.modules.keys())}")
            # Prevent infinite recursion
            if module_name != 'home':
                self.transition_to('home')  # Fallback to home
            else:
                log.critical("Home module not found! Cannot recover.")

    def _update_navigation_highlight(self, module_name: str):
        """Update navigation pane to highlight current module."""
        # Update NavPane through the right side bar manager
        if hasattr(self.main_window, 'right_side_bar_manager'):
            nav_pane = self.main_window.right_side_bar_manager.get_nav_pane()
            if nav_pane:
                success = nav_pane.highlight_item(module_name)
                if not success:
                    log.debug(f"No navigation button found for module '{module_name}' - this is fine")
        elif hasattr(self.main_window, 'nav_pane'):
            # Fallback for older structure
            self.main_window.nav_pane.highlight_item(module_name)
    
    def get_current_module(self) -> Optional[Any]:
        """Get the currently active module."""
        return self.current_module
