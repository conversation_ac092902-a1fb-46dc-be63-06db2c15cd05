# Refactoring Plan: Implementing a Stable Eager Loading Pattern

**Objective**: To refactor the application to use an eager loading pattern, eliminating navigation delays by pre-loading all modules at startup. This plan addresses the root cause of previous UI instability (Qt widget deletion) by introducing a robust lifecycle management system.

**Updated**: 2025-07-15 - Comprehensive analysis of current architecture completed

---

## 1. Problem Definition: Lazy Loading and UI Instability

### Current Architecture Analysis:
*   **ModuleCoordinator**: Located at `flatmate/src/fm/module_coordinator.py` - uses lazy loading with lambda factories
*   **BaseModuleView**: Exists at `flatmate/src/fm/modules/base/base_module_view.py` - provides solid foundation for UI management
*   **Navigation System**: Well-structured signal chain: NavPane → RightSideBarManager → MainWindow → ModuleCoordinator
*   **No BasePresenter**: Currently missing - presenters are independent classes
*   **Performance Bottleneck**: 2.3s UI freeze during categorize module navigation due to table view rendering

### The Blocker - Qt Widget Lifecycle Issues:
Previous attempts failed due to `RuntimeError: Internal C++ object already deleted` crashes. Root cause: <PERSON><PERSON>'s memory manager deletes module UI widgets when removed from main window, but our code attempts to reuse these destroyed objects on re-navigation.

## 2. The Solution: Enhanced Module Lifecycle with Proper Widget Management

The core solution separates one-time setup from repeatable actions, with sophisticated widget lifecycle management that prevents Qt object deletion.

### New Lifecycle Phases:

1.  **`setup()` (One-Time)**: Called once at application startup. Creates view, builds UI widgets, connects signals.
2.  **`show(**params)` (On Navigation)**: Called every navigation. Handles widget placement in main window and content refresh.
3.  **`hide()` (On Navigation)**: Called when leaving module. Removes widgets from main window without destroying them.

### Key Insight - BaseModuleView Integration:
The existing `BaseModuleView.setup_in_main_window()` method must be made **idempotent** (safe to call multiple times) to support the eager loading pattern.

## 3. Detailed Refactoring Steps

### Step 1: Create BasePresenter at Module Level

**Location**: `flatmate/src/fm/modules/base/base_presenter.py`

**Rationale**: Keep base module components at module level (not core) since they're module-specific, not core framework components.

**Implementation**:
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

class BasePresenter(ABC):
    """Base class for all module presenters implementing eager loading lifecycle."""

    def __init__(self, main_window):
        self.main_window = main_window
        self.view = None
        self._is_setup = False
        self._is_visible = False

    @abstractmethod
    def _create_view(self):
        """Create the view instance. Called once during setup."""
        pass

    @abstractmethod
    def _connect_signals(self):
        """Connect view signals to handlers. Called once during setup."""
        pass

    def setup(self):
        """One-time setup: create view, connect signals, build UI."""
        if self._is_setup:
            return

        self.view = self._create_view()
        self._connect_signals()
        self._is_setup = True

    def show(self, **params):
        """Show module and refresh content."""
        if not self._is_setup:
            self.setup()

        # Set up in main window (this handles widget placement)
        self.view.setup_in_main_window(self.main_window)
        self._refresh_content(**params)
        self._is_visible = True

    def hide(self):
        """Hide module but preserve state."""
        if self.view and hasattr(self.view, 'cleanup_from_main_window'):
            self.view.cleanup_from_main_window()
        self._is_visible = False

    def _refresh_content(self, **params):
        """Override to refresh module content when shown."""
        pass

    # # >> When shown? where is the: if self.is_visible ?
    
``` 


### Step 2: Enhance BaseModuleView for Widget Lifecycle Management

**Location**: Modify existing `flatmate/src/fm/modules/base/base_module_view.py`

**Key Enhancement**: Make `setup_in_main_window()` idempotent and add cleanup method:

```python
def setup_in_main_window(self, main_window):
    """Setup in main window - safe to call multiple times."""
    if self.main_window == main_window and hasattr(self, '_is_setup_in_window'):
        # Already set up in this window, just show panels
        self._show_panels()
        return

    # First time setup or different window
    self.main_window = main_window
    self._setup_panels()  # Existing logic
    self._is_setup_in_window = True

def cleanup_from_main_window(self):
    """Remove widgets from main window without destroying them."""
    # Hide panels instead of clearing them to prevent Qt object deletion
    if self.main_window:
        if hasattr(self.main_window, 'left_panel') and self.left_panel:
            self.main_window.left_panel.hide()
        if hasattr(self.main_window, 'center_panel') and self.center_panel:
            self.main_window.center_panel.hide()
        if hasattr(self.main_window, 'right_panel') and self.right_panel:
            self.main_window.right_panel.hide()

def _show_panels(self):
    """Show existing panels."""
    if self.left_panel:
        self.left_panel.show()
    if self.center_panel:
        self.center_panel.show()
    if self.right_panel:
        self.right_panel.show()
```

### Step 3: Refactor ModuleCoordinator for Eager Loading

**Location**: `flatmate/src/fm/module_coordinator.py`

**Key Changes**:
1. Replace `module_factories` with `modules` (actual instances)
2. Add eager loading in `initialize_modules()`
3. Simplify `transition_to()` to show/hide pattern

```python
def initialize_modules(self):
    """Create and setup all modules at startup (eager loading)."""
    log.info("Creating all modules (eager loading)")

    # Create all module instances
    self.modules = {
        'home': HomePresenter(self.main_window),
        'update_data': UpdateDataPresenter(self.main_window),
        'categorize': CategorizePresenter(self.main_window)
    }

    # Setup all modules (one-time UI creation)
    for module_name, module in self.modules.items():
        log.info(f"Setting up {module_name} module")
        module.setup()
        self._connect_module_transitions(module)

    log.info("All modules created and configured")

def transition_to(self, module_name: str, **params):
    """Transition between pre-built modules."""
    actual_module_name = self.NAV_TO_MODULE_MAP.get(module_name, module_name)

    # Hide current module
    if self.current_module:
        self.current_module.hide()

    # Show new module
    if actual_module_name in self.modules:
        self.current_module = self.modules[actual_module_name]
        self.current_module.show(**params)

        # Update navigation (existing logic)
        if hasattr(self.main_window, 'right_side_bar_manager'):
            nav_pane = self.main_window.right_side_bar_manager.get_nav_pane()
            nav_pane.highlight_item(actual_module_name)
```

### Step 4: Refactor Individual Presenters

All presenters must inherit from BasePresenter and implement the new lifecycle.

#### 4.1 HomePresenter Example
**Location**: `flatmate/src/fm/modules/home/<USER>

```python
from ..base.base_presenter import BasePresenter

class HomePresenter(BasePresenter):
    def _create_view(self):
        return HomeView()

    def _connect_signals(self):
        self.view.update_data_clicked.connect(
            lambda: self.request_transition("update_data")
        )
        self.view.categorize_clicked.connect(
            lambda: self.request_transition("categorize")
        )
        # ... other signal connections

    def _refresh_content(self, **params):
        """Refresh home content when shown."""
        if self.state.is_first_run:
            self.view.show_splash_screen()
            QTimer.singleShot(2000, lambda: self.view.show_default_content(WELCOME_CONTENT))
            self.state.mark_first_run_complete()
        else:
            self.view.show_default_content(WELCOME_CONTENT)
```

#### 4.2 CategorizePresenter Example
**Location**: `flatmate/src/fm/modules/categorize/cat_presenter.py`

```python
from ..base.base_presenter import BasePresenter

class CategorizePresenter(BasePresenter):
    def _create_view(self):
        return CatView()

    def _connect_signals(self):
        self.view.files_selected.connect(self._on_files_selected)
        self.view.load_db_requested.connect(self._on_load_db_requested)
        # ... other connections

    def _refresh_content(self, **params):
        """Refresh categorize content - expensive operation happens here."""
        # This method contains what was previously in initialize()
        # The expensive table loading happens here, but only when module is shown
        self._load_transactions_from_db(params)
```

#### 4.3 UpdateDataPresenter Example
**Location**: `flatmate/src/fm/modules/update_data/ud_presenter.py`

```python
from ..base.base_presenter import BasePresenter

class UpdateDataPresenter(BasePresenter):
    def _create_view(self):
        return UpdateDataView(self.main_window)

    def _connect_signals(self):
        self.view.source_select_requested.connect(self._on_source_select)
        # ... other connections

    def _refresh_content(self, **params):
        """Refresh update data content when shown."""
        # Set up view state from config
        self._setup_view_from_config()
        # Show info bar
        self.info_bar_service.show()
        self.info_bar_service.publish_message("Select source files or folder to begin.", "INFO")
```

## 4. Navigation System Integration

### 4.1 Current Navigation Works Perfectly
The existing navigation system requires **no changes**:

**Signal Chain**: `NavPane.navigationSelected` → `RightSideBarManager.navigationSelected` → `ModuleCoordinator.transition_to`

This chain remains unchanged and will work seamlessly with eager loading.

### 4.2 Alternative: Event-Based Navigation (Optional)
If desired, we could implement the event-based system mentioned in the architecture docs, but the current signal-based system is actually cleaner and more direct for this use case.

## 5. Implementation Strategy

### 5.1 Recommended Implementation Order:
1. **Create BasePresenter** - establishes the new lifecycle pattern
2. **Enhance BaseModuleView** - adds widget lifecycle management
3. **Update ModuleCoordinator** - implements eager loading
4. **Refactor HomePresenter first** - simplest module to test with
5. **Refactor remaining presenters** - apply pattern to all modules
6. **Test and optimize** - ensure no memory leaks or performance issues

### 5.2 Testing Strategy:
- **Memory monitoring**: Track memory usage during navigation
- **Performance testing**: Verify elimination of 2.3s delay
- **State preservation**: Ensure user context maintained between navigations
- **Error handling**: Test edge cases and error conditions

## 6. Expected Benefits

1.  **Instant Navigation**: Eliminates 2.3s navigation delay entirely
2.  **UI Stability**: Prevents Qt crashes through proper widget lifecycle management
3.  **State Preservation**: User context (scroll position, selections, etc.) naturally preserved
4.  **Cleaner Architecture**: Better separation of one-time setup vs. refresh logic
5.  **Memory Efficiency**: Acceptable overhead (~2-3MB per module) for significant UX improvement

## 7. Risk Mitigation

### 7.1 Memory Management:
- **Monitoring**: Implement memory usage tracking
- **Cleanup**: Proper cleanup methods prevent memory leaks
- **Limits**: Environment service can assess available RAM and set limits

### 7.2 Performance Considerations:
- **Startup time**: Longer app startup but acceptable vs. navigation delays
- **Background loading**: Could implement background threading for startup if needed
- **Incremental loading**: Can implement module-by-module loading if required

### 7.3 Rollback Strategy:
- **Incremental implementation**: Can be done module by module
- **Fallback**: Easy to revert to lazy loading if issues arise
- **Testing**: Thorough testing at each step prevents major issues
