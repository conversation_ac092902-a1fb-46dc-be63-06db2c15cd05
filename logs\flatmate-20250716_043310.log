2025-07-16 04:33:10 - [fm.core.services.master_file_service] [INFO] - MasterFileService initialized
2025-07-16 04:33:11 - [fm.core.services.cache_service] [INFO] - CacheService initialized
2025-07-16 04:33:11 - [KiwibankBasicCSVHandler] [DEBUG] - Initialized KiwibankBasicCSVHandler
2025-07-16 04:33:11 - [CoopStandardCSVHandler] [DEBUG] - Initialized CoopStandardCSVHandler
2025-07-16 04:33:11 - [ASBStandardCSVHandler] [DEBUG] - Initialized ASBStandardCSVHandler
2025-07-16 04:33:11 - [fm.modules.update_data.services.local_services] [DEBUG] - UpdateDataServices initialized
2025-07-16 04:33:12 - [fm.core.config.base_local_config_v2] [DEBUG] - Loaded config hierarchy for categorize: 0 component defaults, 18 user preferences
2025-07-16 04:33:12 - [fm.core.config.base_local_config_v2] [DEBUG] - CategorizeConfig for categorize initialized.
2025-07-16 04:33:12 - [fm.main] [INFO] - Application starting...
2025-07-16 04:33:13 - [fm.main] [INFO] - 
=== Setting up Module Coordinator ===
2025-07-16 04:33:13 - [fm.module_coordinator] [INFO] - Initializing Module Coordinator
2025-07-16 04:33:13 - [fm.module_coordinator] [DEBUG] - Loaded recent modules: ['home', 'categorize', 'update_data']
2025-07-16 04:33:13 - [fm.module_coordinator] [INFO] - Creating all modules (eager loading)
2025-07-16 04:33:13 - [fm.modules.base.base_presenter] [DEBUG] - Initialized HomePresenter
2025-07-16 04:33:13 - [fm.modules.home.home_presenter] [DEBUG] - Home Presenter initialization complete
2025-07-16 04:33:13 - [fm.modules.base.base_presenter] [DEBUG] - Initialized UpdateDataPresenter
2025-07-16 04:33:13 - [fm.modules.base.base_presenter] [DEBUG] - Initialized CategorizePresenter
2025-07-16 04:33:13 - [fm.core.database.sql_repository.sqlite_repository] [INFO] - Using database at: C:\Users\<USER>\.flatmate\data\transactions.db
2025-07-16 04:33:13 - [fm.core.data_services.db_io_service] [DEBUG] - DBIOService initialized with 383MB cache limit
2025-07-16 04:33:13 - [fm.module_coordinator] [INFO] - Setting up home module
2025-07-16 04:33:13 - [fm.modules.base.base_presenter] [INFO] - Setting up HomePresenter
2025-07-16 04:33:13 - [fm.main] [CRITICAL] - Fatal error during application initialization: name '_get_gui_config' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 58, in initialize_application
    coordinator.initialize_modules()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 59, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 75, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 36, in _create_view
    return HomeView()
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py", line 28, in __init__
    super().__init__(parent)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 84, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py", line 73, in setup_ui
    self.left_panel, left_layout = self.create_left_panel_container()
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 127, in create_left_panel_container
    last_width = self._get_left_panel_width()
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 281, in _get_left_panel_width
    gui_config = _get_gui_config()
                 ^^^^^^^^^^^^^^^
NameError: name '_get_gui_config' is not defined
2025-07-16 04:33:13 - [fm.main] [CRITICAL] - Exception details: Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 58, in initialize_application
    coordinator.initialize_modules()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 59, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 75, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 36, in _create_view
    return HomeView()
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py", line 28, in __init__
    super().__init__(parent)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 84, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py", line 73, in setup_ui
    self.left_panel, left_layout = self.create_left_panel_container()
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 127, in create_left_panel_container
    last_width = self._get_left_panel_width()
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 281, in _get_left_panel_width
    gui_config = _get_gui_config()
                 ^^^^^^^^^^^^^^^
NameError: name '_get_gui_config' is not defined
Traceback (most recent call last):
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\main.py", line 58, in initialize_application
    coordinator.initialize_modules()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\module_coordinator.py", line 59, in initialize_modules
    module.setup()
    ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_presenter.py", line 75, in setup
    self.view = self._create_view()
                ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_presenter.py", line 36, in _create_view
    return HomeView()
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py", line 28, in __init__
    super().__init__(parent)
    ~~~~~~~~~~~~~~~~^^^^^^^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 84, in __init__
    self.setup_ui()
    ~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\home\home_view.py", line 73, in setup_ui
    self.left_panel, left_layout = self.create_left_panel_container()
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 127, in create_left_panel_container
    last_width = self._get_left_panel_width()
  File "C:\Users\<USER>\_DEV\__PROJECTS\Flatmate_Dev\flatmate\src\fm\modules\base\base_module_view.py", line 281, in _get_left_panel_width
    gui_config = _get_gui_config()
                 ^^^^^^^^^^^^^^^
NameError: name '_get_gui_config' is not defined
