"""
Base module view implementation.
Provides standard functionality for all module views.
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QFrame
from PySide6.QtCore import Signal, Qt
from PySide6.QtGui import QResizeEvent

from ...core.services.logger import log

class PanelFrame(QFrame):
    """Frame with resize event handling for panels."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.NoFrame)
    
    def resizeEvent(self, event: QResizeEvent) -> None:
        """Handle resize events and save panel width."""
        super().resizeEvent(event)
        
        # Only save if width actually changed
        if event.oldSize().width() != event.size().width():
            gui_config = _config_provider.get_gui_config()
            GuiKeys = _config_provider.get_gui_keys()
            min_width = gui_config.get_value('gui.window.min_panel_width', default=240)
            if self.objectName() == "left_panel":
                width = max(min_width, self.width())
                gui_config.set_value(GuiKeys.Panel.LEFT_LAST_WIDTH, width)
            elif self.objectName() == "right_panel":
                width = max(min_width, self.width())
                gui_config.set_value(GuiKeys.Panel.RIGHT_LAST_WIDTH, width)


class BaseModuleView(QWidget):
    """Base class for all module views."""
    
    def __init__(self, parent=None):
        """Initialize the view."""
        super().__init__(parent)
        self.main_window = None
        self.left_panel = None
        self.right_panel = None

        # Eager loading support - track setup state
        self._is_setup_in_window = False
        self._panel_containers = {
            'left': None,
            'center': None,
            'right': None
        }

        self.setup_ui()
    
    #------------------
    # Core Methods
    #------------------
    def setup_ui(self):
        """Each module must implement its own UI setup.
        
        This method should:
        1. Create and set up the content_area widget
        2. Set up any module-specific UI elements
        
        Raises:
            NotImplementedError: If subclass doesn't implement this method
        """
        raise NotImplementedError("Subclasses must implement setup_ui()")
    
    def setup_left_panel(self, layout):
        """Set up the left panel content. Override if needed."""
        pass
    
    def setup_center_panel(self, layout):
        """Set up the center panel content. Override if needed."""
        pass

    def setup_right_panel(self, layout):
        """Set up the right panel content. Override if needed."""
        pass
    
    def disconnect_signals(self):
        """Clean up signal connections. Override if you have signals to disconnect."""
        pass
    
    #------------------
    # Panel Creation
    #------------------
    def create_left_panel_container(self):
        """Create standard left panel container."""
        container = PanelFrame()
        container.setObjectName("left_panel")
        self.left_panel = container
        
        # Restore last width if set, otherwise use default
        last_width = self._get_left_panel_width()
        container.resize(last_width, container.height())
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(25)
        return container, layout
    
    def create_center_panel_container(self):
        """Create standard center panel container."""
        container = QFrame()
        container.setObjectName("center_panel_content")
        container.setFrameStyle(QFrame.Shape.NoFrame)
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        return container, layout
    
    def create_right_panel_container(self):
        """Create standard right panel container."""
        container = PanelFrame()
        container.setObjectName("right_panel")
        self.right_panel = container
        
        # Restore last width if set, otherwise use default
        last_width = self._get_right_panel_width()
        container.resize(last_width, container.height())
        
        layout = QVBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(25)
        return container, layout
        

    
    #------------------
    # Main Window Integration
    #------------------
    def setup_in_main_window(self, main_window):
        """Standard setup in main window - idempotent for eager loading.

        This method is now safe to call multiple times. On first call,
        it creates and sets up panels. On subsequent calls, it just
        shows the existing panels.

        Args:
            main_window: The main window instance
        """
        # Check if already set up in this window
        if self.main_window == main_window and self._is_setup_in_window:
            log.debug(f"{self.__class__.__name__} already set up in window, showing panels")
            self._show_panels()
            return

        # First time setup or different window
        log.info(f"Setting up {self.__class__.__name__} in Main Window")
        self.main_window = main_window

        # Create and set up panels only if they have content
        self._setup_panels()

        # Mark as set up in this window
        self._is_setup_in_window = True
        log.info(f"{self.__class__.__name__} setup complete")

    def _setup_panels(self):
        """Internal method to create and set up panels."""
        # Test if panels have content by checking if their setup methods are overridden
        has_left = self.setup_left_panel.__func__ is not BaseModuleView.setup_left_panel
        has_center = self.setup_center_panel.__func__ is not BaseModuleView.setup_center_panel
        has_right = self.setup_right_panel.__func__ is not BaseModuleView.setup_right_panel

        if has_left:
            log.debug("Setting up Left Panel")
            left_container, left_layout = self.create_left_panel_container()
            self.setup_left_panel(left_layout)
            self.main_window.set_left_panel_content(left_container)
            self._panel_containers['left'] = left_container

        if has_center:
            log.debug("Setting up Center Panel")
            center_container, center_layout = self.create_center_panel_container()
            self.setup_center_panel(center_layout)
            self.main_window.set_center_panel_content(center_container)
            self._panel_containers['center'] = center_container

        if has_right:
            log.debug("Setting up Right Panel")
            right_container, right_layout = self.create_right_panel_container()
            self.setup_right_panel(right_layout)
            self.main_window.set_right_panel_content(right_container)
            self._panel_containers['right'] = right_container
    
    def _show_panels(self):
        """Show existing panels - used for eager loading navigation."""
        log.debug(f"Showing panels for {self.__class__.__name__}")

        # Show panels that exist
        for panel_name, container in self._panel_containers.items():
            if container:
                container.show()
                log.debug(f"Showed {panel_name} panel")

    def cleanup_from_main_window(self):
        """Remove widgets from main window without destroying them.

        This method is used during eager loading navigation to hide
        the current module's widgets without destroying them, preventing
        Qt object deletion crashes.
        """
        if not self.main_window:
            return

        log.debug(f"Cleaning up {self.__class__.__name__} from main window")

        # Hide panels instead of clearing them to prevent Qt object deletion
        for panel_name, container in self._panel_containers.items():
            if container:
                container.hide()
                log.debug(f"Hid {panel_name} panel")

    def cleanup(self):
        """Standard cleanup procedure for final shutdown.

        Order is important:
        1. Disconnect signals first to prevent any callbacks during cleanup
        2. Let MainWindow handle widget deletion
        3. Clear references
        """
        if self.main_window:
            # Disconnect signals first to prevent callbacks during cleanup
            if self.disconnect_signals.__func__ is not BaseModuleView.disconnect_signals:
                self.disconnect_signals()

            # Let MainWindow handle widget deletion
            self.main_window.clear_all_panels()

            # Clear references
            self._panel_containers = {'left': None, 'center': None, 'right': None}
            self._is_setup_in_window = False
            self.main_window = None
    
    def _save_panel_sizes(self):
        """Save current panel sizes to config."""
        gui_config = _config_provider.get_gui_config()
        GuiKeys = _config_provider.get_gui_keys()
        if self.left_panel and self.left_panel.isVisible():
            gui_config.set_value(GuiKeys.Panel.LEFT_LAST_WIDTH, self.left_panel.width())
        if self.right_panel and self.right_panel.isVisible():
            gui_config.set_value(GuiKeys.Panel.RIGHT_LAST_WIDTH, self.right_panel.width())
    
    def _get_left_panel_width(self) -> int:
        """Get width for left panel."""
        gui_config = _config_provider.get_gui_config()
        GuiKeys = _config_provider.get_gui_keys()
        min_width = gui_config.get_value('gui.window.min_panel_width', default=240)
        return max(
            min_width,
            gui_config.get_value(
                GuiKeys.Panel.LEFT_LAST_WIDTH,
                default=gui_config.get_value(GuiKeys.Panel.LEFT_DEFAULT_WIDTH)
            )
        )
    
    def _get_right_panel_width(self) -> int:
        """Get width for right panel."""
        gui_config = _config_provider.get_gui_config()
        GuiKeys = _config_provider.get_gui_keys()
        min_width = gui_config.get_value('gui.window.min_panel_width', default=240)
        return max(
            min_width,
            gui_config.get_value(
                GuiKeys.Panel.RIGHT_LAST_WIDTH,
                default=gui_config.get_value(GuiKeys.Panel.RIGHT_DEFAULT_WIDTH)
            )
        )

"""
Implementation Notes:
-------------------

1. Module View Structure
   - BaseModuleView inherits from QWidget 
   - Only setup_ui() is required to be implemented by subclasses
   - Panel setup methods (left, center, right) are optional
   - Signal disconnection is optional but recommended if signals are used

2. Initialization Flow
   - __init__ calls setup_ui() first
   - When placed in main window:
     a. setup_in_main_window() is called
     b. Checks which panel methods are overridden
     c. Only creates panels that have content
     d. Calls appropriate setup methods with layouts

3. Panel Management
   - Each panel (left, center, right) follows the same pattern:
     a. Container creation with standard styling
     b. Layout setup with consistent margins
     c. Optional content setup by subclass
   - Panels are only created if their setup method is overridden
   - This prevents empty panels taking up space

4. Cleanup Pattern
   - Follows Qt's parent-child cleanup pattern
   - Order is important:
     1. Disconnect signals first (prevents callbacks during cleanup)
     2. Let MainWindow handle widget deletion
     3. Clear main_window reference
   - Relies on Qt's automatic cleanup for most widgets

5. Best Practices
   - Create widgets in setup_ui()
   - Place widgets in panel setup methods
   - Override disconnect_signals() if using signals
   - Let Qt handle widget deletion
   - Clear references to prevent circular references

6. Usage Example:
   class MyModuleView(BaseModuleView):
       def setup_ui(self):
           # Create widgets
           self.my_button = QPushButton("Click Me")
           
       def setup_left_panel(self, layout):
           # Place widgets
           layout.addWidget(self.my_button)
           
       def disconnect_signals(self):
           # Clean up signals
           self.my_button.clicked.disconnect()
"""
